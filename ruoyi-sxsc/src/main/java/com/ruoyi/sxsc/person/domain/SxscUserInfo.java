package com.ruoyi.sxsc.person.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUserMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 人员基本信息对象
 * 
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SxscUserInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @TableId(type = IdType.INPUT)
    private Long userId;

    /** 父级主键 */
    private Long parentId;

    /** 节点主键 */
    private Long nodeId;

    /** 推荐时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date parentTime;

    /** 节点时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nodeTime;

    /** 身份证姓名 */
    @Excel(name = "身份证姓名")
    private String identityName;

    /** 身份证性别 */
    @Excel(name = "身份证性别")
    private String identitySex;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String identityNumber;

    /** 身份证有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "身份证有效期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date identityDate;

    /** 支付宝姓名 */
    @Excel(name = "支付宝姓名")
    private String aliPayName;

    /** 支付宝账号 */
    @Excel(name = "支付宝账号")
    private String aliPayAcc;

    /** 微信姓名 */
    @Excel(name = "微信姓名")
    private String wechatName;

    /** 微信标识 */
    @Excel(name = "微信标识")
    private String wechatOpenId;

    /** 贡献值 */
    @Excel(name = "贡献值")
    private BigDecimal integralGxz;

    /** 企业股 */
    @Excel(name = "企业股")
    private BigDecimal integralJl;

    /** 权证 */
    @Excel(name = "权证")
    private BigDecimal integralSzqz;

    /** 权益值 */
    @Excel(name = "权益值")
    private BigDecimal integralSj;

    /** 积分 */
    @Excel(name = "积分")
    private BigDecimal integralTy;

    /** 承兑额度 */
    @Excel(name = "承兑额度")
    private BigDecimal acceptorAmount;

    /** 用户信息 */
    @TableField(exist = false)
    private SysUserMain sysUser;

    /** 邀请人信息 */
    @TableField(exist = false)
    private SysUserMain inviterSysUser;

    /** 节点人信息 */
    @TableField(exist = false)
    private SysUserMain nodeSysUser;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long delFlag;

    /** 认证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "认证时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date identityTime;

    /** 承兑商1是0否 */
    @Excel(name = "承兑商1是0否")
    private Long nodeAcc;

    /** 是否有效用户 */
    @Excel(name = "是否有效用户1是0否")
    private Long effective;

    /** 集团股份 */
    @Excel(name = "集团股份")
    private BigDecimal share;

    /** 承兑商冻结优惠券数量 */
    @Excel(name = "承兑商冻结优惠券数量")
    private BigDecimal nodeAccFreeze;

    /** 盲盒分值 */
    @Excel(name = "盲盒分值")
    private BigDecimal blindBox;

    /** 票证账号 */
    @Excel(name = "票证账号")
    private String ticketAcc;

    /** 合伙人身份1初级2中级3高级4联创5共识 */
    @Excel(name = "合伙人身份")
    private Long copartner;

    /** 合伙人业绩 */
    @Excel(name = "合伙人业绩")
    private BigDecimal copartnerAmount;

    /** PT地址 */
    @Excel(name = "PT地址")
    private String ptAddress;

    /** 积分释放比例 */
    @Excel(name = "积分释放比例")
    private BigDecimal integralReleaseRatio;
}
